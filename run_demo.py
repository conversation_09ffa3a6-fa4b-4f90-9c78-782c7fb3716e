# 运行柔性作业车间调度演示
from fjsp_example_demo import FJSPExampleDemo

# 创建演示实例并运行
demo = FJSPExampleDemo()
results = demo.run_complete_demo()

# 额外的决策逻辑解释
print("\n" + "=" * 60)
print("=== 关键调度决策点分析 ===")
print("\n1. 初始阶段 (t=0):")
print("   所有工件的第一道工序都准备就绪")
print("   • FIFO: 按工件编号顺序选择工件1")
print("   • MOPNR: 选择剩余操作数最多的工件(工件1和3都是3道工序)")
print("   • SPT: 选择最短加工时间的操作-机器组合")
print("   • MWKR: 选择剩余工作量最大的工件")

print("\n2. 中期调度 (多个操作同时就绪):")
print("   当多个工件的操作同时准备就绪时，不同规则的选择策略:")
print("   • FIFO: 严格按就绪时间排序")
print("   • MOPNR: 优先考虑剩余操作数，平衡工件进度")
print("   • SPT: 不考虑工件，只看当前最短加工时间")
print("   • MWKR: 考虑未来工作量，避免重工件积压")

print("\n3. 机器选择策略:")
print("   • FIFO/MOPNR/MWKR: 先选操作，再选最早可用机器")
print("   • SPT: 同时考虑操作和机器，选择全局最短时间")

print("\n4. 适用场景建议:")
print("   • FIFO: 公平性要求高，简单易实现的场景")
print("   • MOPNR: 工件操作数差异大，需要平衡进度的场景")
print("   • SPT: 优先完成短任务，提高系统响应速度的场景")
print("   • MWKR: 工件加工时间差异大，需要避免重工件积压的场景")