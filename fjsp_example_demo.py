import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from typing import List, Tu<PERSON>, Dict

class FJSPExampleDemo:
    def __init__(self):
        """
        柔性作业车间调度问题实例演示
        使用简化的3工件、2-3道工序、3台机器的小规模实例
        """
        self.num_machines = 3
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        
        # 定义实例数据：3工件，每工件2-3道工序，3台机器
        self.jobs = [
            # 工件1：3道工序
            [
                [(0, 3), (1, 2), (2, 4)],  # 工序1：机器1用时3，机器2用时2，机器3用时4
                [(0, 2), (2, 3)],          # 工序2：机器1用时2，机器3用时3
                [(1, 4), (2, 2)]           # 工序3：机器2用时4，机器3用时2
            ],
            # 工件2：2道工序
            [
                [(0, 4), (1, 3)],          # 工序1：机器1用时4，机器2用时3
                [(1, 2), (2, 5)]           # 工序2：机器2用时2，机器3用时5
            ],
            # 工件3：3道工序
            [
                [(1, 2), (2, 3)],          # 工序1：机器2用时2，机器3用时3
                [(0, 3), (1, 4)],          # 工序2：机器1用时3，机器2用时4
                [(0, 2), (2, 4)]           # 工序3：机器1用时2，机器3用时4
            ]
        ]
        
        print("=== 实例数据说明 ===")
        print("工件数：3，机器数：3")
        for i, job in enumerate(self.jobs):
            print(f"工件{i+1}：{len(job)}道工序")
            for j, op in enumerate(job):
                machines_info = [f"机器{m+1}({t}min)" for m, t in op]
                print(f"  工序{j+1}：{', '.join(machines_info)}")
        print()

    def fifo_detailed_demo(self):
        """FIFO调度规则详细演示"""
        print("=== FIFO (先到先服务) 调度演示 ===")
        print("规则：优先调度最早准备就绪的操作\n")
        
        # 初始化
        machine_available = [0, 0, 0]
        job_next_op = [0, 0, 0]
        job_last_completion = [0, 0, 0]
        schedule = []
        step = 1
        
        while sum(job_next_op[i] < len(self.jobs[i]) for i in range(3)) > 0:
            print(f"--- 调度步骤 {step} ---")
            
            # 找到准备就绪的操作
            ready_ops = []
            for job_id in range(3):
                if job_next_op[job_id] < len(self.jobs[job_id]):
                    ready_time = job_last_completion[job_id]
                    ready_ops.append((ready_time, job_id, job_next_op[job_id]))
            
            print("当前可调度操作：")
            for ready_time, job_id, op_id in ready_ops:
                print(f"  工件{job_id+1}-工序{op_id+1}，就绪时间：{ready_time}")
            
            # FIFO选择：最早就绪的操作
            ready_ops.sort()
            selected_ready_time, selected_job, selected_op = ready_ops[0]
            
            print(f"FIFO选择：工件{selected_job+1}-工序{selected_op+1} (就绪时间最早：{selected_ready_time})")
            
            # 机器分配：选择最早可用的兼容机器
            op_machines = self.jobs[selected_job][selected_op]
            best_machine, best_start, best_duration = None, float('inf'), 0
            
            print("机器选择分析：")
            for machine_id, duration in op_machines:
                start_time = max(machine_available[machine_id], selected_ready_time)
                print(f"  机器{machine_id+1}：可用时间{machine_available[machine_id]}，开始时间{start_time}，加工时长{duration}")
                if start_time < best_start:
                    best_start, best_machine, best_duration = start_time, machine_id, duration
            
            completion_time = best_start + best_duration
            print(f"选择机器{best_machine+1}：开始{best_start}，结束{completion_time}\n")
            
            # 更新状态
            machine_available[best_machine] = completion_time
            job_last_completion[selected_job] = completion_time
            job_next_op[selected_job] += 1
            
            schedule.append({
                'job': selected_job, 'operation': selected_op, 'machine': best_machine,
                'start': best_start, 'duration': best_duration, 'end': completion_time
            })
            
            step += 1
        
        makespan = max(machine_available)
        print(f"FIFO总完工时间：{makespan}\n")
        return schedule, makespan

    def mopnr_detailed_demo(self):
        """MOPNR调度规则详细演示"""
        print("=== MOPNR (剩余操作数最多优先) 调度演示 ===")
        print("规则：优先调度剩余操作数最多的工件\n")
        
        machine_available = [0, 0, 0]
        job_next_op = [0, 0, 0]
        job_last_completion = [0, 0, 0]
        schedule = []
        step = 1
        
        while sum(job_next_op[i] < len(self.jobs[i]) for i in range(3)) > 0:
            print(f"--- 调度步骤 {step} ---")
            
            # 找到准备就绪的操作并计算剩余操作数
            ready_ops = []
            for job_id in range(3):
                if job_next_op[job_id] < len(self.jobs[job_id]):
                    ready_time = job_last_completion[job_id]
                    remaining_ops = len(self.jobs[job_id]) - job_next_op[job_id] - 1
                    ready_ops.append((remaining_ops, ready_time, job_id, job_next_op[job_id]))
            
            print("当前可调度操作及剩余操作数：")
            for remaining, ready_time, job_id, op_id in ready_ops:
                total_ops = len(self.jobs[job_id])
                current_op = op_id + 1
                print(f"  工件{job_id+1}-工序{current_op}，剩余操作数：{remaining} (总{total_ops}道工序，当前第{current_op}道)")
            
            # MOPNR选择：剩余操作数最多的
            ready_ops.sort(key=lambda x: (-x[0], x[1], x[2], x[3]))
            selected_remaining, selected_ready_time, selected_job, selected_op = ready_ops[0]
            
            print(f"MOPNR选择：工件{selected_job+1}-工序{selected_op+1} (剩余操作数最多：{selected_remaining})")
            
            # 机器分配
            op_machines = self.jobs[selected_job][selected_op]
            best_machine, best_start, best_duration = None, float('inf'), 0
            
            for machine_id, duration in op_machines:
                start_time = max(machine_available[machine_id], selected_ready_time)
                if start_time < best_start:
                    best_start, best_machine, best_duration = start_time, machine_id, duration
            
            completion_time = best_start + best_duration
            print(f"分配到机器{best_machine+1}：开始{best_start}，结束{completion_time}\n")
            
            # 更新状态
            machine_available[best_machine] = completion_time
            job_last_completion[selected_job] = completion_time
            job_next_op[selected_job] += 1
            
            schedule.append({
                'job': selected_job, 'operation': selected_op, 'machine': best_machine,
                'start': best_start, 'duration': best_duration, 'end': completion_time
            })
            
            step += 1
        
        makespan = max(machine_available)
        print(f"MOPNR总完工时间：{makespan}\n")
        return schedule, makespan

    def spt_detailed_demo(self):
        """SPT调度规则详细演示"""
        print("=== SPT (最短加工时间优先) 调度演示 ===")
        print("规则：选择当前可用的操作-机器组合中加工时间最短的\n")
        
        machine_available = [0, 0, 0]
        job_next_op = [0, 0, 0]
        job_last_completion = [0, 0, 0]
        schedule = []
        step = 1
        
        while sum(job_next_op[i] < len(self.jobs[i]) for i in range(3)) > 0:
            print(f"--- 调度步骤 {step} ---")
            
            # 找到所有准备就绪的操作-机器对
            ready_pairs = []
            for job_id in range(3):
                if job_next_op[job_id] < len(self.jobs[job_id]):
                    ready_time = job_last_completion[job_id]
                    op_machines = self.jobs[job_id][job_next_op[job_id]]
                    
                    for machine_id, duration in op_machines:
                        start_time = max(machine_available[machine_id], ready_time)
                        ready_pairs.append((duration, start_time, job_id, job_next_op[job_id], machine_id))
            
            print("当前可调度的操作-机器对：")
            for duration, start_time, job_id, op_id, machine_id in ready_pairs:
                print(f"  工件{job_id+1}-工序{op_id+1} 在机器{machine_id+1}：加工时长{duration}，开始时间{start_time}")
            
            # SPT选择：加工时间最短的组合
            ready_pairs.sort()
            selected_duration, selected_start, selected_job, selected_op, selected_machine = ready_pairs[0]
            
            print(f"SPT选择：工件{selected_job+1}-工序{selected_op+1} 在机器{selected_machine+1} (加工时间最短：{selected_duration})")
            
            completion_time = selected_start + selected_duration
            print(f"调度结果：开始{selected_start}，结束{completion_time}\n")
            
            # 更新状态
            machine_available[selected_machine] = completion_time
            job_last_completion[selected_job] = completion_time
            job_next_op[selected_job] += 1
            
            schedule.append({
                'job': selected_job, 'operation': selected_op, 'machine': selected_machine,
                'start': selected_start, 'duration': selected_duration, 'end': completion_time
            })
            
            step += 1
        
        makespan = max(machine_available)
        print(f"SPT总完工时间：{makespan}\n")
        return schedule, makespan

    def mwkr_detailed_demo(self):
        """MWKR调度规则详细演示"""
        print("=== MWKR (剩余工作量最多优先) 调度演示 ===")
        print("规则：优先调度剩余工作量最大的工件\n")
        
        machine_available = [0, 0, 0]
        job_next_op = [0, 0, 0]
        job_last_completion = [0, 0, 0]
        schedule = []
        step = 1
        
        while sum(job_next_op[i] < len(self.jobs[i]) for i in range(3)) > 0:
            print(f"--- 调度步骤 {step} ---")
            
            # 计算剩余工作量
            ready_ops = []
            for job_id in range(3):
                if job_next_op[job_id] < len(self.jobs[job_id]):
                    ready_time = job_last_completion[job_id]
                    
                    # 计算剩余工作量（包括当前操作）
                    remaining_work = 0
                    for op_idx in range(job_next_op[job_id], len(self.jobs[job_id])):
                        op_machines = self.jobs[job_id][op_idx]
                        avg_time = sum(duration for _, duration in op_machines) / len(op_machines)
                        remaining_work += avg_time
                    
                    ready_ops.append((remaining_work, ready_time, job_id, job_next_op[job_id]))
            
            print("当前可调度操作及剩余工作量：")
            for remaining_work, ready_time, job_id, op_id in ready_ops:
                print(f"  工件{job_id+1}-工序{op_id+1}，剩余工作量：{remaining_work:.1f}")
            
            # MWKR选择：剩余工作量最大的
            ready_ops.sort(key=lambda x: (-x[0], x[1], x[2], x[3]))
            selected_work, selected_ready_time, selected_job, selected_op = ready_ops[0]
            
            print(f"MWKR选择：工件{selected_job+1}-工序{selected_op+1} (剩余工作量最大：{selected_work:.1f})")
            
            # 机器分配
            op_machines = self.jobs[selected_job][selected_op]
            best_machine, best_start, best_duration = None, float('inf'), 0
            
            for machine_id, duration in op_machines:
                start_time = max(machine_available[machine_id], selected_ready_time)
                if start_time < best_start:
                    best_start, best_machine, best_duration = start_time, machine_id, duration
            
            completion_time = best_start + best_duration
            print(f"分配到机器{best_machine+1}：开始{best_start}，结束{completion_time}\n")
            
            # 更新状态
            machine_available[best_machine] = completion_time
            job_last_completion[selected_job] = completion_time
            job_next_op[selected_job] += 1
            
            schedule.append({
                'job': selected_job, 'operation': selected_op, 'machine': best_machine,
                'start': best_start, 'duration': best_duration, 'end': completion_time
            })
            
            step += 1
        
        makespan = max(machine_available)
        print(f"MWKR总完工时间：{makespan}\n")
        return schedule, makespan

    def draw_gantt_chart(self, schedules_dict):
        """绘制四种调度规则的甘特图对比"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('柔性作业车间调度四种规则甘特图对比', fontsize=16, fontweight='bold')
        
        rules = ['FIFO', 'MOPNR', 'SPT', 'MWKR']
        positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
        
        for idx, (rule, (row, col)) in enumerate(zip(rules, positions)):
            ax = axes[row, col]
            schedule, makespan = schedules_dict[rule]
            
            # 绘制甘特图
            for item in schedule:
                job_id = item['job']
                machine_id = item['machine']
                start_time = item['start']
                duration = item['duration']
                operation_id = item['operation']
                
                # 创建矩形块
                rect = patches.Rectangle(
                    (start_time, machine_id), duration, 0.8,
                    linewidth=1, edgecolor='black',
                    facecolor=self.colors[job_id], alpha=0.8
                )
                ax.add_patch(rect)
                
                # 添加标签
                ax.text(start_time + duration/2, machine_id + 0.4, 
                       f'J{job_id+1}-O{operation_id+1}',
                       ha='center', va='center', fontsize=9, fontweight='bold')
            
            # 设置坐标轴
            ax.set_xlim(0, max(makespan + 2, 15))
            ax.set_ylim(-0.5, self.num_machines - 0.5)
            ax.set_xlabel('时间')
            ax.set_ylabel('机器')
            ax.set_title(f'{rule} (Makespan: {makespan})')
            ax.set_yticks(range(self.num_machines))
            ax.set_yticklabels([f'机器{i+1}' for i in range(self.num_machines)])
            ax.grid(True, alpha=0.3)
            
            # 添加makespan线
            ax.axvline(x=makespan, color='red', linestyle='--', linewidth=2, alpha=0.7)
            ax.text(makespan + 0.2, self.num_machines/2, f'Makespan\n{makespan}', 
                   rotation=90, va='center', color='red', fontweight='bold')
        
        # 添加图例
        legend_elements = [patches.Patch(facecolor=self.colors[i], label=f'工件{i+1}') 
                          for i in range(3)]
        fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), 
                  ncol=3, fontsize=12)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, bottom=0.1)
        plt.show()

    def analyze_differences(self, schedules_dict):
        """分析四种调度规则的关键差异"""
        print("=== 四种调度规则对比分析 ===\n")
        
        # 计算性能指标
        results = {}
        for rule, (schedule, makespan) in schedules_dict.items():
            # 机器利用率
            machine_work_time = [0] * self.num_machines
            for item in schedule:
                machine_work_time[item['machine']] += item['duration']
            
            total_work_time = sum(machine_work_time)
            utilization = total_work_time / (self.num_machines * makespan)
            
            # 工件完成时间
            job_completion = [0] * 3
            for item in schedule:
                job_completion[item['job']] = max(job_completion[item['job']], item['end'])
            
            results[rule] = {
                'makespan': makespan,
                'utilization': utilization,
                'job_completion': job_completion,
                'machine_work_time': machine_work_time
            }
        
        # 输出对比结果
        print("1. 总完工时间 (Makespan) 对比：")
        sorted_rules = sorted(results.items(), key=lambda x: x[1]['makespan'])
        for i, (rule, data) in enumerate(sorted_rules):
            rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "4️⃣"
            print(f"   {rank} {rule}: {data['makespan']} 分钟")
        
        print("\n2. 机器利用率对比：")
        for rule, data in results.items():
            print(f"   {rule}: {data['utilization']:.1%}")
            for i, work_time in enumerate(data['machine_work_time']):
                machine_util = work_time / data['makespan']
                print(f"     机器{i+1}: {machine_util:.1%} ({work_time}/{data['makespan']})")
        
        print("\n3. 工件完成时间分布：")
        for rule, data in results.items():
            completion_times = data['job_completion']
            avg_completion = sum(completion_times) / len(completion_times)
            print(f"   {rule}: 工件1={completion_times[0]}, 工件2={completion_times[1]}, "
                  f"工件3={completion_times[2]}, 平均={avg_completion:.1f}")
        
        print("\n4. 关键差异分析：")
        print("   • FIFO: 公平但不一定最优，按到达顺序处理")
        print("   • MOPNR: 平衡工件进度，避免某些工件过早完成")
        print("   • SPT: 优先短任务，可能导致长任务延迟")
        print("   • MWKR: 考虑工作量分布，平衡系统负载")
        
        return results

    def run_complete_demo(self):
        """运行完整的演示"""
        print("柔性作业车间调度问题四种调度规则详细演示\n")
        print("=" * 60)
        
        # 运行四种调度规则
        schedules = {}
        schedules['FIFO'] = self.fifo_detailed_demo()
        schedules['MOPNR'] = self.mopnr_detailed_demo()
        schedules['SPT'] = self.spt_detailed_demo()
        schedules['MWKR'] = self.mwkr_detailed_demo()
        
        # 绘制甘特图
        self.draw_gantt_chart(schedules)
        
        # 分析差异
        self.analyze_differences(schedules)
        
        return schedules

# 运行演示
if __name__ == "__main__":
    demo = FJSPExampleDemo()
    results = demo.run_complete_demo()